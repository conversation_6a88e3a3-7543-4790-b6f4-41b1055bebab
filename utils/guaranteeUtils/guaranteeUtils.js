const { Op, literal } = require('sequelize');
const _ = require('lodash');

const { reportEnums } = require('../../enums');

const { countryToISOMapping, getHigherAndLowerCreditRating } = require('../../utils/creditRatingUtils');
const {
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
  providerCountries,
} = require('../../utils/providerDataUtils');
const { roundToXDecimals } = require('../numbers');
const { assessmentAnswersTemplateData, countryToRegionMapper } = require('../reportUtils');
const { groupByThreeCharacters, truncateToTwoDecimals } = require('../strings');
const { getActualGeneratedFile, getTemplateFile } = require('../templateFilesUtils');
const { companyRepository } = require('../../repositories');
const { BadRequestError, InternalServerError } = require('../../utils/ErrorHandler');
const dateUtils = require('../../utils/dates');
const { TemplateFileLabelsEnum } = require('../../enums/templateFiles');
const { templateConsts } = require('../templateFilesUtils');
const { getCompany } = require('../../repositories/companyRepository');

const getTemplateFilename = (guarantee) => {
  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH) {
    return guarantee.pricingApproach.includes('implicit')
      ? templateConsts.templateFilenames.GUARANTEE_YIELD_EL_IMPLICIT
      : templateConsts.templateFilenames.GUARANTEE_YIELD_EL_STANDALONE;
  }

  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.SECURITY_APPROACH) {
    return guarantee.pricingApproach.includes('implicit')
      ? templateConsts.templateFilenames.GUARANTEE_SECURITY_IMPLICIT
      : templateConsts.templateFilenames.GUARANTEE_SECURITY_STANDALONE;
  }

  throw new InternalServerError('Pricing methodology is not defined');
};

const getGuaranteeReportName = (guarantee, reportType) =>
  `${guarantee.principal.name} ${guarantee.currency} ${groupByThreeCharacters(guarantee.amount)} ${reportType}`;

// same is used to get loan agreement object
const getGuaranteeReportObject = ({
  label,
  guaranteeId,
  name,
  extension = '.docx',
  isGenerated = true,
  mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
}) => ({
  extension,
  isGenerated,
  label,
  guaranteeId,
  mimeType,
  name,
});

async function createTemplateData(data, dateFormat) {
  const { guarantor, principal, isUsingRegionalProviderData, ...guarantee } = data;

  const isSecurityMethodology = reportEnums.pricingMethodologyEnum.SECURITY_APPROACH === guarantee.pricingMethodology;
  const parsedAmount = groupByThreeCharacters(guarantee?.amount);

  const principalSurroundingRatings = getHigherAndLowerCreditRating(principal?.creditRating?.rating);

  const lenderData = await getCompany({ id: guarantor.id }, ['note']);
  const borrowerData = await getCompany({ id: principal.id }, ['note']);

  const commonFields = {
    'unique id': guarantee.id,
    'unique id 2': guarantee.externalId,
    amount: parsedAmount,
    AMOUNT: parsedAmount,
    currency: guarantee?.currency,
    CURRENCY: guarantee?.currency.toUpperCase(),
    'issue date': dateUtils.formatDateString(guarantee?.issueDate, dateFormat ?? 'DD-MM-YYYY'),
    'payment frequency': guarantee?.paymentFrequency,
    note: guarantee?.note,

    'lender note': lenderData.note ?? 'n/a',
    'borrower note': borrowerData.note ?? 'n/a',

    'termination date': dateUtils.formatDateString(guarantee?.terminationDate, dateFormat ?? 'DD-MM-YYYY'),

    'guarantor country': guarantor?.country,
    guarantor: guarantor?.name,

    'principal country': principal?.country,
    'principal industry': principal?.industry,
    'principal rating lower': principalSurroundingRatings?.lowerCreditRating ?? 'n/a',
    'principal rating upper': principalSurroundingRatings?.higherCreditRating ?? 'n/a',
    'principal rating': principal?.creditRating?.rating,
    'principal rating adj': principal?.creditRating?.ratingAdj ?? principal?.creditRating?.rating,
    principal: principal?.name,
    PRINCIPAL: principal?.name.toUpperCase(),

    fee: 'finalInterestRate' in guarantee.report ? roundToXDecimals(String(guarantee.report.finalInterestRate)) : 'n/a',

    'consolidated group rating': data.parentCompanyRating?.rating,

    // 'report date': dateUtils.formatDate(new Date()),
    'report date': dateUtils.formatDateCustom(new Date(), dateFormat ?? 'DD-MM-YYYY'),

    'data geography principal':
      providerCountries.includes(principal?.country) && !isUsingRegionalProviderData
        ? principal?.country
        : countryToRegionMapper[principal?.country],

    'calculation log': isSecurityMethodology
      ? JSON.stringify(guarantee?.calculationLog)
      : guarantee?.calculationLog?.log,
  };

  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH) {
    return { ...commonFields, ...createYieldExpectedLossApproachSpecificTemplateData(data, dateFormat) };
  } else {
    return { ...commonFields, ...createSecurityApproachSpecificTemplateData(guarantee) };
  }
}

function createYieldExpectedLossApproachSpecificTemplateData(data, dateFormat) {
  const { guarantor, principal, isUsingRegionalProviderData, ...guarantee } = data;
  const calculationData = guarantee.calculationLog.data;

  const guarantorSurroundingRatings = getHigherAndLowerCreditRating(guarantor?.creditRating?.rating);

  const lossGivenDefault = getLossGivenDefault(guarantee?.seniority, principal?.industry);
  const guarantorBase = roundToXDecimals(calculationData['guarantor points']?.midPoint);
  const principalBase = roundToXDecimals(calculationData['principal points']?.midPoint);

  const { cumulativeProbabilityOfDefault } = principal.creditRating;

  if (cumulativeProbabilityOfDefault == null) {
    throw new BadRequestError('This guarantee needs to be repriced.');
  }

  return {
    seniority: guarantee?.seniority,

    period: calculationData?.tenor ? truncateToTwoDecimals(parseFloat(calculationData?.tenor)) : 'n/a',
    'period months': calculationData?.tenor ? Math.round((calculationData?.tenor % 1) * 12) : 'n/a',
    'period years': calculationData?.tenor ? Math.floor(calculationData?.tenor) : 'n/a',
    'period shorter': calculationData['tenor shorter'] ?? 'n/a',
    'period longer': calculationData['tenor longer'] ?? 'n/a',

    'guarantor industry': guarantor?.industry,
    'guarantor rating lower': guarantorSurroundingRatings?.lowerCreditRating ?? 'n/a',
    'guarantor rating upper': guarantorSurroundingRatings?.higherCreditRating ?? 'n/a',
    'guarantor rating adj': guarantor?.creditRating?.ratingAdj ?? guarantor?.creditRating?.rating,
    'guarantor rating': guarantor?.creditRating?.rating,

    GTU: calculationData['guarantor upper points']?.midPoint
      ? roundToXDecimals(calculationData['guarantor upper points']?.midPoint)
      : 'n/a',
    GSU: calculationData['guarantor upper points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['guarantor upper points']?.lowerBasePoint)
      : 'n/a',
    GLU: calculationData['guarantor upper points']?.higherBasePoint
      ? roundToXDecimals(calculationData['guarantor upper points']?.higherBasePoint)
      : 'n/a',

    GTL: calculationData['guarantor lower points']?.midPoint
      ? roundToXDecimals(calculationData['guarantor lower points']?.midPoint)
      : 'n/a',
    GSL: calculationData['guarantor lower points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['guarantor lower points']?.lowerBasePoint)
      : 'n/a',
    GLL: calculationData['guarantor lower points']?.higherBasePoint
      ? roundToXDecimals(calculationData['guarantor lower points']?.higherBasePoint)
      : 'n/a',

    'guarantor base': guarantorBase,
    GSM: calculationData['guarantor points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['guarantor points']?.lowerBasePoint)
      : 'n/a',
    GLM: calculationData['guarantor points']?.higherBasePoint
      ? roundToXDecimals(calculationData['guarantor points']?.higherBasePoint)
      : 'n/a',

    PTU: calculationData['principal upper points']?.midPoint
      ? roundToXDecimals(calculationData['principal upper points']?.midPoint)
      : 'n/a',
    PSU: calculationData['principal upper points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['principal upper points']?.lowerBasePoint)
      : 'n/a',
    PLU: calculationData['principal upper points']?.higherBasePoint
      ? roundToXDecimals(calculationData['principal upper points']?.higherBasePoint)
      : 'n/a',

    PTL: calculationData['principal lower points']?.midPoint
      ? roundToXDecimals(calculationData['principal lower points']?.midPoint)
      : 'n/a',
    PSL: calculationData['principal lower points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['principal lower points']?.lowerBasePoint)
      : 'n/a',
    PLL: calculationData['principal lower points']?.higherBasePoint
      ? roundToXDecimals(calculationData['principal lower points']?.higherBasePoint)
      : 'n/a',

    'principal base': principalBase,
    PSM: calculationData['principal points']?.lowerBasePoint
      ? roundToXDecimals(calculationData['principal points']?.lowerBasePoint)
      : 'n/a',
    PLM: calculationData['principal points']?.higherBasePoint
      ? roundToXDecimals(calculationData['principal points']?.higherBasePoint)
      : 'n/a',

    lgd: lossGivenDefault * 100,
    pd:
      principal?.creditRating?.probabilityOfDefault != null
        ? roundToXDecimals(principal?.creditRating?.probabilityOfDefault)
        : 'n/a',
    'pd adj':
      principal?.creditRating?.probabilityOfDefaultAdj != null
        ? roundToXDecimals(principal?.creditRating?.probabilityOfDefaultAdj)
        : 'n/a',
    'cm pd': cumulativeProbabilityOfDefault != null ? roundToXDecimals(cumulativeProbabilityOfDefault) : 'n/a',
    'cm pd adj': cumulativeProbabilityOfDefault != null ? roundToXDecimals(cumulativeProbabilityOfDefault) : 'n/a',

    el: guarantee?.report?.lowerBound,

    spread: guarantee?.report?.upperBound,

    // 'data date': dateUtils.formatDate(calculationData?.dataDate),
    'data date': dateUtils.formatDateString(calculationData?.dataDate, dateFormat ?? 'DD-MM-YYYY'),

    'data geography guarantor':
      providerCountries.includes(guarantor?.country) && !isUsingRegionalProviderData
        ? guarantor?.country
        : countryToRegionMapper[guarantor?.country],
  };
}

function createSecurityApproachSpecificTemplateData(guarantee, dateFormat) {
  const { report, calculationLog } = guarantee;

  const { data: unsubordinatedCalculationData } = calculationLog[0].unsubordinated;
  const { data: subordinatedCalculationData } = calculationLog[1].subordinated;
  const { data: seniorSecuredCalculationData } = calculationLog[2].seniorSecured;

  return {
    period: unsubordinatedCalculationData?.tenor
      ? truncateToTwoDecimals(parseFloat(unsubordinatedCalculationData?.tenor))
      : 'n/a',
    'period months': unsubordinatedCalculationData?.tenor
      ? Math.round((unsubordinatedCalculationData?.tenor % 1) * 12)
      : 'n/a',
    'period years': unsubordinatedCalculationData?.tenor ? Math.floor(unsubordinatedCalculationData?.tenor) : 'n/a',
    'period shorter': unsubordinatedCalculationData['tenor shorter'] ?? 'n/a',
    'period longer': unsubordinatedCalculationData['tenor longer'] ?? 'n/a',

    se: seniorSecuredCalculationData['principal points']?.midPoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal points']?.midPoint)
      : 'n/a',
    un: unsubordinatedCalculationData['principal points']?.midPoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal points']?.midPoint)
      : 'n/a',
    su: subordinatedCalculationData['principal points']?.midPoint
      ? roundToXDecimals(subordinatedCalculationData['principal points']?.midPoint)
      : 'n/a',

    ss: roundToXDecimals(report.upperBound),
    us: roundToXDecimals(report.lowerBound),

    'ss adj': roundToXDecimals(report.upperBound),
    'us adj': roundToXDecimals(report.lowerBound),

    PSLSS: seniorSecuredCalculationData['principal lower points']?.lowerBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal lower points']?.lowerBasePoint)
      : 'n/a',
    PSLUN: unsubordinatedCalculationData['principal lower points']?.lowerBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal lower points']?.lowerBasePoint)
      : 'n/a',
    PSLSU: subordinatedCalculationData['principal lower points']?.lowerBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal lower points']?.lowerBasePoint)
      : 'n/a',

    PSMSS: seniorSecuredCalculationData['principal points']?.lowerBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal points']?.lowerBasePoint)
      : 'n/a',
    PSMUN: unsubordinatedCalculationData['principal points']?.lowerBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal points']?.lowerBasePoint)
      : 'n/a',
    PSMSU: subordinatedCalculationData['principal points']?.lowerBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal points']?.lowerBasePoint)
      : 'n/a',

    PSUSS: seniorSecuredCalculationData['principal upper points']?.lowerBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal upper points']?.lowerBasePoint)
      : 'n/a',
    PSUUN: unsubordinatedCalculationData['principal upper points']?.lowerBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal upper points']?.lowerBasePoint)
      : 'n/a',
    PSUSU: subordinatedCalculationData['principal upper points']?.lowerBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal upper points']?.lowerBasePoint)
      : 'n/a',

    PTLSS: seniorSecuredCalculationData['principal lower points']?.midPoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal lower points']?.midPoint)
      : 'n/a',
    PTLUN: unsubordinatedCalculationData['principal lower points']?.midPoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal lower points']?.midPoint)
      : 'n/a',
    PTLSU: subordinatedCalculationData['principal lower points']?.midPoint
      ? roundToXDecimals(subordinatedCalculationData['principal lower points']?.midPoint)
      : 'n/a',

    PTUSS: seniorSecuredCalculationData['principal upper points']?.midPoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal upper points']?.midPoint)
      : 'n/a',
    PTUUN: unsubordinatedCalculationData['principal upper points']?.midPoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal upper points']?.midPoint)
      : 'n/a',
    PTUSU: subordinatedCalculationData['principal upper points']?.midPoint
      ? roundToXDecimals(subordinatedCalculationData['principal upper points']?.midPoint)
      : 'n/a',

    PLLSS: seniorSecuredCalculationData['principal lower points']?.higherBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal lower points']?.higherBasePoint)
      : 'n/a',
    PLLUN: unsubordinatedCalculationData['principal lower points']?.higherBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal lower points']?.higherBasePoint)
      : 'n/a',
    PLLSU: subordinatedCalculationData['principal lower points']?.higherBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal lower points']?.higherBasePoint)
      : 'n/a',

    PLMSS: seniorSecuredCalculationData['principal points']?.higherBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal points']?.higherBasePoint)
      : 'n/a',
    PLMUN: unsubordinatedCalculationData['principal points']?.higherBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal points']?.higherBasePoint)
      : 'n/a',
    PLMSU: subordinatedCalculationData['principal points']?.higherBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal points']?.higherBasePoint)
      : 'n/a',

    PLUSS: seniorSecuredCalculationData['principal upper points']?.higherBasePoint
      ? roundToXDecimals(seniorSecuredCalculationData['principal upper points']?.higherBasePoint)
      : 'n/a',
    PLUUN: unsubordinatedCalculationData['principal upper points']?.higherBasePoint
      ? roundToXDecimals(unsubordinatedCalculationData['principal upper points']?.higherBasePoint)
      : 'n/a',
    PLUSU: subordinatedCalculationData['principal upper points']?.higherBasePoint
      ? roundToXDecimals(subordinatedCalculationData['principal upper points']?.higherBasePoint)
      : 'n/a',

    // 'data date': dateUtils.formatDate(unsubordinatedCalculationData?.dataDate),
    'data date': dateUtils.formatDateString(unsubordinatedCalculationData?.dataDate, dateFormat ?? 'DD-MM-YYYY'),
  };
}

async function createTemplateDataImplicit(
  guarantee,
  guarantorAssessment,
  principalAssessment,
  parentCompanyRating,
  dateFormat,
) {
  const templateData = await createTemplateData(guarantee, dateFormat);
  const commonTemplateData = {
    ...templateData,
    'consolidated group rating': parentCompanyRating?.rating,
    'principal assessment': principalAssessment?.name ?? 'n/a',
    ...assessmentAnswersTemplateData(principalAssessment?.answers, false),
  };

  if (guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.SECURITY_APPROACH) {
    return {
      ...commonTemplateData,
      'guarantor assessment': guarantorAssessment?.name ?? 'n/a',
      ...assessmentAnswersTemplateData(guarantorAssessment?.answers, true),
    };
  } else return commonTemplateData;
}

async function generateAgreement(guarantee, clientId, templateData) {
  const dbClientTemplateFileAgreement = await getTemplateFile({
    clientId,
    country: guarantee.principal.country,
    companyId: guarantee.principal.id,
    label: TemplateFileLabelsEnum.GUARANTEE_AGREEMENT,
  });
  const agreementFilename = templateConsts.templateFilenames.GUARANTEE_INTERCOMPANY_AGREEMENT;

  const agreementFile = await getActualGeneratedFile(dbClientTemplateFileAgreement, templateData, agreementFilename);

  return [
    agreementFile,
    getGuaranteeReportObject({
      guaranteeId: guarantee.id,
      label: 'Agreement',
      name: getGuaranteeReportName(guarantee, 'Guarantee Agreement'),
    }),
  ];
}

async function generateTpReport(guarantee, clientId, templateData) {
  let label;
  if (guarantee.pricingApproach.includes('implicit')) {
    label =
      guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH
        ? TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT
        : TemplateFileLabelsEnum.GUARANTEE_SECURITY_ADJUSTED_REPORT;
  } else {
    label =
      guarantee.pricingMethodology === reportEnums.pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH
        ? TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT
        : TemplateFileLabelsEnum.GUARANTEE_SECURITY_STANDALONE_REPORT;
  }

  const dbClientTemplateFileReport = await getTemplateFile({
    clientId,
    country: guarantee.principal.country,
    companyId: guarantee.principal.id,
    label,
  });
  const tpReportFilename = getTemplateFilename(guarantee);

  const tpReportFile = await getActualGeneratedFile(dbClientTemplateFileReport, templateData, tpReportFilename);

  return [
    tpReportFile,
    getGuaranteeReportObject({
      guaranteeId: guarantee.id,
      label: 'TP Report',
      name: getGuaranteeReportName(guarantee, 'Guarantee Report'),
    }),
  ];
}

function overrideCreditRatings(guarantorCreditRating, principalCreditRating) {
  const overridenGuarantorCreditRating = guarantorCreditRating;
  const overridenPrincipalCreditRating = principalCreditRating;
  if (guarantorCreditRating?.newRating) {
    overridenGuarantorCreditRating.rating = overridenGuarantorCreditRating?.newRating;
    delete overridenGuarantorCreditRating.newRating;
  }

  if (principalCreditRating?.newRating) {
    overridenPrincipalCreditRating.rating = overridenPrincipalCreditRating?.newRating;
    delete overridenPrincipalCreditRating.newRating;
  }

  return [overridenGuarantorCreditRating, overridenPrincipalCreditRating];
}

// TODO add permission object for those countries and so on
function createProviderSearchData({
  guarantee,
  guarantor,
  principal,
  providerDataIssueDate,
  isUsingRegionalProviderData,
}) {
  const guarantorSearchData = {
    issueDate: providerDataIssueDate,
    region: regionAbbreviations[countryToRegionMapper[guarantor.country]],
    country:
      providerCountries.includes(guarantor.country) && !isUsingRegionalProviderData
        ? countryToISOMapping[guarantor.country]
        : 'ALL',
    industryGroup: industryAbbreviations[guarantor.industry],
    currency: guarantee.currency,
    seniority: seniorityAbbreviations[guarantee.seniority],
  };
  const principalSearchData = {
    issueDate: providerDataIssueDate,
    region: regionAbbreviations[countryToRegionMapper[principal.country]],
    country:
      providerCountries.includes(principal.country) && !isUsingRegionalProviderData
        ? countryToISOMapping[principal.country]
        : 'ALL',
    industryGroup: industryAbbreviations[principal.industry],
    currency: guarantee.currency,
    seniority: seniorityAbbreviations[guarantee.seniority],
  };

  return { guarantorSearchData, principalSearchData };
}

function getLossGivenDefault(seniority, industry) {
  if (seniority === 'Senior Secured') {
    return 0.15;
  }

  if (seniority === 'Unsubordinated') {
    if (industry === 'Financials') {
      return 0.45;
    }
    return 0.4;
  }

  return 0.75;
}

function calculateSecurityApproachBounds({ seniorSecuredMidPoint, unsubordinatedMidPoint, subordinatedMidPoint }) {
  const upperBound = subordinatedMidPoint - seniorSecuredMidPoint;
  const lowerBound = unsubordinatedMidPoint - seniorSecuredMidPoint;
  const midPoint = (upperBound + lowerBound) / 2;

  /**
   * For Security Approach the bounds should be sorted by default, that is lowerBound should be the
   * lowest and the upperBound should be the highest. So this check is made to catch edge cases
   * if any appear while testing.
   */
  const unsortedBounds = [lowerBound, midPoint, upperBound];
  const sortedBounds = _.sortBy([upperBound, lowerBound, midPoint]);

  if (!_.isEqual(sortedBounds, unsortedBounds)) {
    throw new InternalServerError('Bounds are not correct.');
  }

  return sortedBounds;
}

const getTemplateData = async (guarantee, clientId, isUsingRegionalProviderData, dateFormat) => {
  const parentCompanyRating = await companyRepository.getCompany(
    { id: { [Op.eq]: literal('"parentCompanyId"') }, clientId },
    ['creditRating'],
  );
  if (guarantee.pricingApproach.includes('implicit')) {
    const [guarantorAssessment, principalAssessment] = await Promise.all([
      companyRepository.getCompany({ id: guarantee.guarantor.id }, ['assessment']),
      companyRepository.getCompany({ id: guarantee.principal.id }, ['assessment']),
    ]);
    return createTemplateDataImplicit(
      { ...guarantee, isUsingRegionalProviderData },
      guarantorAssessment.assessment,
      principalAssessment.assessment,
      parentCompanyRating.creditRating,
      dateFormat,
    );
  }
  return createTemplateData(
    { ...guarantee, isUsingRegionalProviderData, parentCompanyRating: parentCompanyRating.creditRating },
    dateFormat,
  );
};

module.exports = {
  overrideCreditRatings,
  createProviderSearchData,
  getLossGivenDefault,
  generateAgreement,
  generateTpReport,
  calculateSecurityApproachBounds,
  getTemplateData,
};
