import { literal, Op } from 'sequelize';

import { cashPoolEnums, Client } from '../../enums';
import models from '../../models';
import {
  cashPoolBatchFileRepository,
  cashPoolParticipantTrailRepository,
  cashPoolRepository,
} from '../../repositories';
import { CashPoolPaymentSheetData, GetCashPoolReturnType, OrderByType } from '../../types';
import { checkClientFeatureFlags } from '../../utils/clientUtils';
import { BadRequestError, NotFoundError } from '../ErrorHandler';
import { roundToXDecimals } from '../numbers';
const { CashPool, Company, CashPoolBatch, Cash_Pool_Participants, ParticipantAccountIds } = models;

const getBatchStatus = (paidPaymentsCount: number, totalPaymentsCount: number) => {
  if (paidPaymentsCount === 0) return cashPoolEnums.batchStatus.UNPAID;
  if (paidPaymentsCount === totalPaymentsCount) return cashPoolEnums.batchStatus.PAID;
  return cashPoolEnums.batchStatus.PARTIALLY_PAID;
};

const getCashPoolBatchFileData = ({
  cashPoolBatchId,
  originalname,
  mimetype,
}: {
  cashPoolBatchId: number;
  originalname: string;
  mimetype: string;
}) => {
  const originalNameMatch = originalname.match(/(.+?)(\.[^.]*$|$)/)!;

  return { cashPoolBatchId, name: originalNameMatch[1], extension: originalNameMatch[2], mimeType: mimetype };
};

const getCashPoolPaymentsSheetData = (payments: Array<any>, columns: Array<string>, clientName: string) => {
  return payments.map((payment: any) => {
    const result = {} as CashPoolPaymentSheetData;

    const {
      interestPayable,
      interestReceivable,
      currency,
      isPaid,
      creditor,
      debtor,
      batch,
      participantAccount,
      trailsData,
    } = payment;
    const { cashPool } = batch;

    const interestPayment = interestPayable || interestReceivable;
    const standaloneInterestRate = interestPayable
      ? participantAccount.debitInterestRate
      : participantAccount.creditInterestRate;
    const unit = cashPool.interestType === 'fixed' ? '%' : ' basis points';
    const rateType = participantAccount?.topCurrencyAccount?.interestType;
    const interestAdded = participantAccount.generateInterestStatementData ? 'Yes' : 'No';
    const externalIds = participantAccount.externalIds;

    for (const column of columns) {
      switch (column) {
        case 'Cash Pool':
          result['Cash Pool'] = cashPool.name;
          continue;
        case 'Creditor':
          result['Creditor'] = creditor.company.name;
          continue;
        case 'Debtor':
          result['Debtor'] = debtor.company.name;
          continue;
        case 'Date Range':
          result['Date Range'] = `${new Date(batch.startDate).toLocaleDateString()} - ${new Date(
            batch.endDate,
          ).toLocaleDateString()}`;
          continue;
        case 'Participant Initial Balance':
          result['Participant Initial Balance'] =
            trailsData?.initialBalance != null ? roundToXDecimals(trailsData.initialBalance, 2) : '-';
          continue;
        case 'Participant Ending Balance':
          result['Participant Ending Balance'] =
            trailsData?.endingBalance != null ? roundToXDecimals(trailsData.endingBalance, 2) : '-';
          continue;
        case 'Participant Average Balance':
          result['Participant Average Balance'] =
            trailsData?.averageBalance != null ? roundToXDecimals(trailsData.averageBalance, 2) : '-';
          continue;
        case 'Effective Period Interest Rate':
          result['Effective Period Interest Rate'] =
            trailsData?.effectivePeriodInterestRate != null
              ? roundToXDecimals(trailsData.effectivePeriodInterestRate, 6)
              : '-';
          continue;
        case 'Currency':
          result['Currency'] = currency;
          continue;
        case 'Interest':
          result['Interest'] = roundToXDecimals(interestPayment, 2);
          continue;
        case 'Interest Due':
          result['Interest Due'] = batch.endDate;
          continue;
        case 'Status':
          result['Status'] = isPaid ? 'Published' : 'Unpublished';
          continue;
        case 'Standalone Rate':
          result['Standalone Interest Rate'] = `${roundToXDecimals(standaloneInterestRate, 2)}${unit}`;
          continue;
        case 'Rate Type':
          result['Rate Type'] = rateType;
          continue;
        case 'Interest Added':
          result['Interest Added'] = interestAdded;
          continue;
        case 'External ID':
          if (checkClientFeatureFlags(Client.GUNVOR, clientName))
            result['External ID'] =
              creditor.id === participantAccount.participant.id
                ? externalIds[0].externalId
                : externalIds[0].externalId.replace(/^(.*?_)([A-Z0-9]{3})([A-Z0-9]{3})(_.*)$/, '$1$3$2$4');
          else result['External ID'] = externalIds.map((id: typeof ParticipantAccountIds) => id.externalId).join(', ');
          continue;
        case 'Unique ID':
          result['Unique ID'] = participantAccount.participant.uniqueId || '';
          continue;
        default:
          continue;
      }
    }

    return result;
  });
};

const cashPoolMapping = (field: string, ordering: OrderByType) => {
  if (field === 'creditor' || field === 'debtor') {
    return [[{ model: Cash_Pool_Participants, as: field }, { model: Company, as: 'company' }, 'name', ordering]];
  }

  if (field === 'paymentDue') {
    return [[{ model: CashPoolBatch, as: 'batch' }, 'endDate', ordering]];
  }

  if (field === 'isPaid') {
    return 'isPaid';
  }

  if (field === 'currency') {
    return [[{ model: CashPoolBatch, as: 'batch' }, { model: CashPool, as: 'cashPool' }, 'currencies', ordering]];
  }

  if (field === 'interestPaymentString') {
    return [[literal('"interestPayableReceivable"'), ordering]];
  }
};

const getOrderBy = (orderBy: any) => {
  if (!orderBy) return;

  const ordering = orderBy.split('+')[1] || 'asc';
  const mapped = cashPoolMapping(orderBy.split('+')[0], ordering);

  if (!mapped) return;

  // id added because for example start/end dates are usually same so the ordering doesn't change
  if (typeof mapped === 'string') {
    return [[mapped, ordering], 'id'];
  }

  return [...mapped, 'id'];
};

const getFilterBy = (filterBy: any) => {
  const START_DATE = 'startDate';
  const END_DATE = 'endDate';
  const CREDITOR = 'creditor';
  const DEBTOR = 'debtor';
  const BATCH_ID = 'batchId';

  const allowedBatchFilters = ['cashPoolId', BATCH_ID, START_DATE, END_DATE];
  const allowedPaymentsFilters = ['isPaid', 'currency'];
  const allowedCreditorDebtorFilters = [CREDITOR, DEBTOR];

  const batchFilter: Record<string, any> = {};
  const paymentFilter: Record<string, string | boolean> = {};
  const creditorFilter: Record<string, number> = {};
  const debtorFilter: Record<string, number> = {};

  for (const [key, value] of Object.entries<any>(filterBy)) {
    if (!key || !value) {
      continue;
    }

    if (allowedBatchFilters.includes(key)) {
      if (key === START_DATE) {
        batchFilter[START_DATE] = { [Op.gte]: new Date(value) };
        continue;
      }
      if (key === END_DATE) {
        batchFilter[END_DATE] = { [Op.lte]: new Date(value) };
        continue;
      }
      if (key === BATCH_ID) {
        batchFilter['id'] = value;
        continue;
      }
      batchFilter[key] = value;
      continue;
    }

    if (allowedPaymentsFilters.includes(key)) {
      paymentFilter[key] = value;
      continue;
    }

    if (allowedCreditorDebtorFilters.includes(key)) {
      /**
       * Both creditor and debtor are participants. They are defined depending on who pays who.
       * If interestPayable is defined (not null) then the leader is creditor and participant is debtor.
       * Otherwise if interestPayable is not defined then participant is creditor and leader is debtor.
       * If interestPayable is not defined the interestReceivable is and vice versa
       */
      if (key === CREDITOR) {
        creditorFilter['companyId'] = value;
        continue;
      }
      if (key === DEBTOR) {
        debtorFilter['companyId'] = value;
        continue;
      }
    }
  }

  return [paymentFilter, batchFilter, creditorFilter, debtorFilter];
};

const groupAccountsByTopCurrencyAccountId = (batchFileInJson: any) => {
  const TOP_CURRENCY_ACCOUNT_ID_COLUMN = 4;
  const accountsByTopCurrencyId: Record<string, Array<[Date, string, number, 'n/a' | number, number]>> = {};
  for (const row of batchFileInJson) {
    const topCurrencyAccountId: string = row[TOP_CURRENCY_ACCOUNT_ID_COLUMN];

    if (!accountsByTopCurrencyId[topCurrencyAccountId]) accountsByTopCurrencyId[topCurrencyAccountId] = [];

    accountsByTopCurrencyId[topCurrencyAccountId].push(row);
  }
  return accountsByTopCurrencyId;
};

const runPoolCashGuards = async ({
  clientId,
  cashPoolId,
  batchId,
}: {
  clientId: number;
  cashPoolId: number;
  batchId: number;
}): Promise<GetCashPoolReturnType> => {
  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) {
    throw new NotFoundError('Cash pool');
  }

  const batchFile = await cashPoolBatchFileRepository.getCashPoolBatchFile({ clientId, cashPoolId, batchId });
  if (!batchFile) {
    throw new NotFoundError('Batch file');
  }

  const trails = await cashPoolParticipantTrailRepository.getCashPoolParticipantTrailsByBatchId({ batchId, limit: 1 });
  if (trails.length > 0) {
    throw new BadRequestError('Cash already pooled for this file');
  }

  return cashPool;
};

export = {
  getBatchStatus,
  getCashPoolBatchFileData,
  getCashPoolPaymentsSheetData,
  getFilterBy,
  getOrderBy,
  groupAccountsByTopCurrencyAccountId,
  runPoolCashGuards,
};
