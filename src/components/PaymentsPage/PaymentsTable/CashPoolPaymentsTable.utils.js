import { getCashPoolPayments, markCashPoolPaymentAsPaid } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { updateField } from '~/reducers/payment.slice';
import { Client } from '~/types';
import { Box, Text } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { checkClientFeatureFlags } from '~/utils/clientFeatureFlags';
import { formatDateString } from '~/utils/dates';
import { errorHandler } from '~/utils/errors';
import { capitalize, displayNumber2 } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';

function getCashPoolData(payment, userInfo) {
  const { dateFormat, decimalPoint, client } = userInfo;
  const { batch, participantAccount, interestPayable, interestReceivable, currency, trailsData } = payment;
  const { cashPool } = batch;
  const { leader } = cashPool;
  const interestPayment = interestPayable ?? interestReceivable;
  const creditor = interestPayable ? leader.name : participantAccount.participant.company.name;
  const debtor = interestPayable ? participantAccount.participant.company?.name : leader.name;
  const standaloneRate = interestPayable ? participantAccount.debitInterestRate : participantAccount.creditInterestRate;
  const interestType = participantAccount?.topCurrencyAccount?.interestType;
  const unit = interestType === 'fixed' ? '%' : ' bps';
  const numberDisplayOptions = { decimalPoint, minDig: 2, maxDig: 2 };
  const numberDisplayOptionsWithUnit = { ...numberDisplayOptions, unit };
  const isInterestUsedInBatch = !!payment?.statementData?.cashPoolBatchId;

  return {
    clientName: client.name,
    paymentId: payment.id,
    batchId: batch.id,
    cashPoolId: cashPool.id,
    cashPool: cashPool.name,
    isInterestUsedInBatch,
    generateInterestStatementData: participantAccount.generateInterestStatementData,
    creditor,
    debtor,
    dateRange: `${formatDateString(batch.startDate, dateFormat)} - ${formatDateString(batch.endDate, dateFormat)}`,
    initialBalance:
      trailsData?.initialBalance != null ? displayNumber2(trailsData.initialBalance, numberDisplayOptions) : '-',
    endingBalance:
      trailsData?.endingBalance != null ? displayNumber2(trailsData.endingBalance, numberDisplayOptions) : '-',
    averageBalance:
      trailsData?.averageBalance != null ? displayNumber2(trailsData.averageBalance, numberDisplayOptions) : '-',
    effectivePeriodInterestRate:
      trailsData?.effectivePeriodInterestRate != null
        ? trailsData.effectivePeriodInterestRate === 0
          ? '0'
          : displayNumber2(trailsData.effectivePeriodInterestRate, {
              ...numberDisplayOptions,
              minDig: 6,
              maxDig: 6,
            })
        : '-',
    currency,
    interestPayment,
    interestPaymentString: displayNumber2(interestPayment, numberDisplayOptions),
    rateType: capitalize(interestType) || 'n/a',
    standaloneRate,
    standaloneRateString: displayNumber2(standaloneRate, numberDisplayOptionsWithUnit),
    paymentDue: `${formatDateString(batch.endDate, dateFormat)}`,
    interestAdded: participantAccount.generateInterestStatementData ? 'Yes' : 'No',
    isPaid: payment.isPaid ? 'Published' : 'Unpublished',
    externalIds: participantAccount.externalIds,
    participantId: participantAccount.participant.id,
    paymentCreditor: payment.creditor,
    uniqueId: participantAccount.participant.uniqueId || '-',
  };
}

export function getCashPoolPaymentsData(data = [], userInfo) {
  return data.map((item) => getCashPoolData(item, userInfo));
}

const columns = [
  { label: 'Cash Pool', sortBy: 'cashPool', value: 'cashPool', width: 250, wrapText: true },
  { label: 'Creditor', sortBy: 'creditor', value: 'creditor', width: 250, wrapText: true },
  { label: 'Debtor', sortBy: 'debtor', value: 'debtor', width: 250, wrapText: true },
  { label: 'Date Range', sortBy: 'dateRange', value: 'dateRange', width: 250 },
  {
    label: 'Participant Initial Balance',
    sortBy: 'initialBalance',
    value: 'initialBalance',
    justifyContent: 'flex-end',
  },
  { label: 'Participant Ending Balance', sortBy: 'endingBalance', value: 'endingBalance', justifyContent: 'flex-end' },
  {
    label: 'Participant Average Balance',
    sortBy: 'averageBalance',
    value: 'averageBalance',
    justifyContent: 'flex-end',
  },
  {
    label: 'Participant Effective Period Interest Rate',
    sortBy: 'effectivePeriodInterestRate',
    value: 'effectivePeriodInterestRate',
    justifyContent: 'flex-end',
  },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  {
    label: 'Interest',
    sortBy: 'interestPaymentString',
    value: 'interestPaymentString',
    justifyContent: 'flex-end',
  },
  { label: 'Rate Type', sortBy: 'rateType', value: 'rateType' },
  { label: 'Standalone Rate', sortBy: 'standaloneRateString', value: 'standaloneRateString' },
  { label: 'Interest Due', sortBy: 'paymentDue', value: 'paymentDue' },
  { label: 'Interest Added', sortBy: 'interestAdded', value: 'interestAdded' },
  {
    label: 'External ID',
    sortBy: 'externalId',
    value: 'externalId',
    renderCustomCell: ({ externalIds, clientName, participantId, paymentCreditor }) => {
      const isClientGunvor = checkClientFeatureFlags(Client.GUNVOR, clientName);

      if (isClientGunvor) {
        return (
          <Text>
            {paymentCreditor.id === participantId
              ? externalIds[0].externalId
              : externalIds[0].externalId.replace(/^(.*?_)([A-Z0-9]{3})([A-Z0-9]{3})(_.*)$/, '$1$3$2$4')}
          </Text>
        );
      }

      return externalIds.map((id, index) => (
        <Box
          key={id.externalId}
          sx={{
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            display: 'block',
            maxWidth: '100%',
          }}
        >
          <Text>{`${id.externalId}${externalIds.length > 1 && index !== externalIds.length - 1 ? ', ' : ''}`}</Text>
        </Box>
      ));
    },
  },
  { label: 'Unique ID', sortBy: 'uniqueId', value: 'uniqueId' },
  {
    label: 'Status',
    sortBy: 'isPaid',
    value: 'isPaid',
    // important isPaid is a string for export
    renderCustomCell: ({ isPaid }) => (
      <Text color={isPaid === 'Published' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
        {isPaid === 'Published' ? 'Published' : 'Unpublished'}
      </Text>
    ),
  },
];

export function getCashPoolPaymentsColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}

export const renderTableActionColumn = ({
  item,
  setPayments,
  searchQuery,
  dispatch,
  setIsAddInterestToBalanceModalShowing,
}) => {
  const { cashPoolId, batchId, paymentId, isInterestUsedInBatch } = item;
  const options = [];

  options.push({ label: 'Show all interest', onClick: () => dispatch(updateField({ batchId })) });

  if (item.isPaid === 'Published' && !isInterestUsedInBatch) {
    options.push({
      label: 'Unpublish',
      onClick: async () => {
        try {
          await markCashPoolPaymentAsPaid({ cashPoolId, batchId, paymentId, data: { isPaid: false } });

          getCashPoolPayments({ searchQuery })
            .then((cashPoolPaymentsWithCount) => setPayments({ cashPoolPaymentsWithCount }))
            .then(() => showToast('Interest unpublished'));
        } catch (error) {
          errorHandler(error);
        }
      },
    });
  }

  if (item.isPaid === 'Unpublished') {
    if (item.generateInterestStatementData) {
      options.push({ label: 'Publish', onClick: () => setIsAddInterestToBalanceModalShowing(item) });
    } else {
      options.push({
        label: 'Publish',
        onClick: async () => {
          await markCashPoolPaymentAsPaid({ cashPoolId, batchId, paymentId, data: { isPaid: true } });

          getCashPoolPayments({ searchQuery })
            .then((cashPoolPaymentsWithCount) => setPayments({ cashPoolPaymentsWithCount }))
            .then(() => showToast('Interest published'));
        },
      });
    }
  }

  return <ThreeDotActionMenu options={options} />;
};

export const cashPoolTooltips = {
  firstCompany: 'Select the company that is in the creditor position.', // creditor
  secondCompany: 'Select the company that is in the debtor position.', // debtor
  currency: 'Select the currency in which the interest is denominated.',
};
