import { ThreeDotActionMenu } from 'components/Shared';
import { StatementDataType, UserInfoType } from 'types';
import { Checkbox } from 'ui';
import { formatDateString } from 'utils/dates';
import { displayNumber2 } from 'utils/strings';

export type TableRowType = {
  id: number;
  companyName: string;
  date?: string;
  statementDate?: string;
  balanceChange: string;
  balanceChangeNotFormatted: number;
  source: string;
  createdAt: string;
  comment: string | null;
  isUsed: 'Yes' | 'No';
};

const sourceMapper = { MANUALLY_ADDED: 'Manual', INTEREST: 'Interest', TEMPLATE_UPLOAD: 'Template Upload' } as const;

function getCashPoolStatementData(data: StatementDataType, userInfo: UserInfoType): TableRowType {
  const { dateFormat, decimalPoint } = userInfo;
  const numberDisplayOptions = { decimalPoint, defaultValue: '-', minDig: 2 };

  return {
    id: data.id,
    companyName: data.account.participant.company.name,
    date: formatDateString(data.date, dateFormat),
    statementDate: formatDateString(data.statementDate, dateFormat) ?? '-',
    balanceChange: displayNumber2(data.balanceChange, numberDisplayOptions),
    balanceChangeNotFormatted: data.balanceChange,
    source: sourceMapper[data.statementNumber as keyof typeof sourceMapper] ?? 'Statement',
    createdAt: formatDateString(data.createdAt, dateFormat) ?? '-',
    comment: data.comment,
    isUsed: data.cashPoolBatchId ? 'Yes' : 'No',
  };
}

export function getCashPoolStatementsData(data: Array<StatementDataType> = [], userInfo: UserInfoType) {
  return data.map((item) => getCashPoolStatementData(item, userInfo));
}

export const columns = ({
  selectedStatementIds,
  toggleCheckbox,
  allSelected,
  toggleAllCheckboxes,
}: {
  selectedStatementIds: number[];
  toggleCheckbox: (id: number, isActive: boolean) => void;
  allSelected: boolean;
  toggleAllCheckboxes: (isActive: boolean) => void;
}) => [
  {
    value: 'checkbox',
    sortBy: '',
    renderCustomCell: ({ id }: { id: number }) => (
      <Checkbox
        isActive={selectedStatementIds.includes(id)}
        onChange={(isActive: boolean) => toggleCheckbox(id, isActive)}
      />
    ),
    label: <Checkbox isActive={allSelected} onChange={toggleAllCheckboxes} />,
    disableSortBy: true,
    width: 50,
  },
  { label: 'Value Date', sortBy: 'date', value: 'date' },
  { label: 'Statement Date (Book Date)', sortBy: 'statementDate', value: 'statementDate' },
  { label: 'Participant', sortBy: 'companyName', value: 'companyName' },
  { label: 'Balance Change', sortBy: 'balanceChange', value: 'balanceChange' },
  { label: 'Source', sortBy: 'source', value: 'source' },
  { label: 'Created Date', sortBy: 'createdAt', value: 'createdAt' },
  { label: 'Used', sortBy: 'isUsed', value: 'isUsed' },
];

export const renderTableActionColumn = ({
  item,
  setIsDeleteModalShowing,
  setIsEditStatementModalShowing,
}: {
  item: TableRowType;
  setIsDeleteModalShowing: React.Dispatch<React.SetStateAction<false | TableRowType>>;
  setIsEditStatementModalShowing: React.Dispatch<React.SetStateAction<false | TableRowType>>;
}) => {
  const options = [];

  options.push({
    label: 'Edit',
    onClick: () => setIsEditStatementModalShowing(item),
  });

  options.push({
    label: 'Delete',
    onClick: () => setIsDeleteModalShowing(item),
  });

  return <ThreeDotActionMenu options={options} />;
};
