import { addMonths } from 'date-fns';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { getCashPoolParticipantTrailsBalanceDate, getCashPoolStructuralPositions } from '~/api';
import { CompanyMultiSelect, SimpleCompanySingleSelect, TopCurrencyAccountSingleSelect } from '~/components/Shared';
import { CHART, NORDIC } from '~/enums';
import { useCompanySelectOptions } from '~/hooks';
import { cashPoolSelector } from '~/reducers/cashPool.slice';
import { cashPoolDataSelector } from '~/reducers/cashPoolData.slice';
import {
  cashPoolStructuralPositionsSelector,
  setDefaultChosenCompanies,
  updateDateRange,
  updateField,
} from '~/reducers/cashPoolStructuralPositions.slice';
import { Card, DateRangeInput, FlexLayout, NumberInput, Text } from '~/ui';
import { errorHandler } from '~/utils/errors';

import CashPoolTableChartPicker from '../CashPoolData/CashPoolTableChartPicker';
import StructuralPositionsChart from './StructuralPositionsChart/StructuralPositionsChart';
import StructuralPositionsTable from './StructuralPositionsTable/StructuralPositionsTable';

const StructuralPositions = () => {
  const dispatch = useDispatch();
  const { cashPoolId } = useParams();
  const { cashPoolUpdateTrigger, type, topCurrencyAccounts } = useSelector(cashPoolSelector);
  const { allParticipants } = useSelector(cashPoolDataSelector);
  const {
    dateRange,
    participantsTrails,
    structuralPositions,
    chosenCompanyId,
    chosenCompanyIds,
    chosenTopCurrencyAccountId,
    selectedView,
    structuralThreshold,
    materialityThreshold,
  } = useSelector(cashPoolStructuralPositionsSelector);
  const getCompanySelectOptions = useCompanySelectOptions(allParticipants, chosenTopCurrencyAccountId, type);

  const isNordic = type === NORDIC;
  const isChartView = selectedView === CHART;

  useEffect(() => {
    const chosenCompanyIds = allParticipants.map(({ companyId }) => companyId);
    dispatch(setDefaultChosenCompanies({ chosenCompanyIds }));
    isNordic && dispatch(updateField({ chosenTopCurrencyAccountId: topCurrencyAccounts[0]?.id }));
  }, [dispatch, allParticipants, isNordic, topCurrencyAccounts]);

  useEffect(() => {
    if (!chosenCompanyId) return;
    if (!dateRange.startDate && !dateRange.endDate) return;

    const data = {
      companyIds: isChartView ? [chosenCompanyId] : chosenCompanyIds,
      startDate: isChartView ? addMonths(new Date(dateRange.startDate), -structuralThreshold) : dateRange.startDate,
      endDate: isChartView ? addMonths(new Date(dateRange.endDate), structuralThreshold) : dateRange.endDate,
      topCurrencyAccountId: chosenTopCurrencyAccountId,
      structuralThreshold,
      materialityThreshold,
    };
    getCashPoolStructuralPositions({ cashPoolId, data }).then((structuralPositions) =>
      dispatch(updateField({ structuralPositions }))
    );
  }, [
    dispatch,
    cashPoolId,
    chosenCompanyId,
    chosenCompanyIds,
    dateRange,
    cashPoolUpdateTrigger,
    chosenTopCurrencyAccountId,
    isChartView,
    structuralThreshold,
    materialityThreshold,
  ]);

  useEffect(() => {
    if (!isChartView) return;
    if (!chosenCompanyId) return;
    if (!dateRange.startDate || !dateRange.endDate) return;

    const data = { companyIds: [chosenCompanyId], ...dateRange, topCurrencyAccountId: chosenTopCurrencyAccountId };
    getCashPoolParticipantTrailsBalanceDate({ cashPoolId, data })
      .then((participantsTrails) =>
        dispatch(updateField({ participantsTrails: participantsTrails.length ? participantsTrails[0] : null }))
      )
      .catch(errorHandler);
  }, [
    dispatch,
    cashPoolId,
    chosenCompanyId,
    dateRange,
    cashPoolUpdateTrigger,
    chosenTopCurrencyAccountId,
    isChartView,
  ]);

  const onTableChartPickerChange = (newSelectedView) => {
    if (newSelectedView === selectedView) return;

    dispatch(updateField({ selectedView: newSelectedView }));
  };

  return (
    <Card p={4}>
      <FlexLayout justify-content="space-between">
        <Text color="deep-sapphire" variant="xl-spaced-bold">
          Structural Positions
        </Text>
      </FlexLayout>
      <FlexLayout justifyContent="space-between">
        <FlexLayout space={3} justifyContent="center" alignItems="center">
          <TopCurrencyAccountSingleSelect
            variant="short"
            width="m"
            height="input-height-small"
            value={chosenTopCurrencyAccountId}
            onChange={(chosenTopCurrencyAccountId) => dispatch(updateField({ chosenTopCurrencyAccountId }))}
            isShowing={isNordic}
          />
          {!isChartView ? (
            <CompanyMultiSelect
              label="Company"
              height="input-height-small"
              options={getCompanySelectOptions()}
              value={chosenCompanyIds}
              onChange={(chosenCompanyIds) => dispatch(updateField({ chosenCompanyIds }))}
              includeSelectAll
            />
          ) : (
            <SimpleCompanySingleSelect
              label="Company"
              height="input-height-small"
              options={getCompanySelectOptions()}
              value={chosenCompanyId}
              onChange={(companyId) => dispatch(updateField({ chosenCompanyId: companyId }))}
            />
          )}
          <DateRangeInput
            dateRange={dateRange}
            onChange={(range) => dispatch(updateDateRange(range))}
            label="Date Range"
            height="input-height-small"
          />
          <NumberInput
            allowNegatives={false}
            label="Duration Threshold"
            value={structuralThreshold}
            height="input-height-small"
            unit="Months"
            tooltip="The number of consecutive months a cash pool position must remain open to be considered structural."
            onChange={(structuralThreshold) => dispatch(updateField({ structuralThreshold }))}
          />
          <NumberInput
            allowNegatives={false}
            label="Materiality Threshold"
            value={materialityThreshold}
            height="input-height-small"
            placeholder="0"
            tooltip="The range around zero where the position is no longer considered non-structural (e.g., between -100,000 and +100,000).<br/>A balance in this zone won't be flagged as structural, and the duration resets."
            onChange={(materialityThreshold) => dispatch(updateField({ materialityThreshold }))}
          />
        </FlexLayout>
        <CashPoolTableChartPicker selectedView={selectedView} setSelectedView={onTableChartPickerChange} />
      </FlexLayout>
      {chosenCompanyIds?.length > 0 && (
        <>
          <StructuralPositionsTable isShowing={!isChartView} structuralPositions={structuralPositions} />
          <StructuralPositionsChart
            isShowing={isChartView}
            structuralPositions={structuralPositions}
            trails={participantsTrails}
          />
        </>
      )}
    </Card>
  );
};

export default StructuralPositions;
